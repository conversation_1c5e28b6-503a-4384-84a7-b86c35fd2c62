import kivy
kivy.require('2.0.0') # Specify Kivy version if necessary

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.scrollview import <PERSON><PERSON><PERSON>iew
from kivy.uix.gridlayout import GridLayout
from kivy.core.clipboard import Clipboard
from kivy.lang import Builder
from kivy.properties import StringProperty, ListProperty, BooleanProperty, ObjectProperty
from kivy.clock import Clock

import requests
import threading
from urllib.parse import urlparse, parse_qs, urlencode
from datetime import datetime

# KV Language string for the UI layout
KV = """
MainLayout:
    orientation: 'vertical'
    spacing: '10dp'
    padding: '10dp'

    BoxLayout:
        size_hint_y: None
        height: '40dp' # Fixed height for this row
        spacing: '5dp'
        Label:
            text: 'Input URL:'
            size_hint_x: 0.2
            halign: 'left'
            valign: 'middle'
            text_size: self.size
        TextInput:
            id: url_input
            multiline: False # Single line input for URL
            font_size: '14sp'
            size_hint_x: 0.7
            hint_text: 'Paste Adjust URL here'
        Button:
            text: 'Paste'
            size_hint_x: 0.1
            on_release: root.paste_url()

    Button:
        id: process_button
        text: '1. Process URL'
        size_hint_y: None
        height: '40dp'
        on_release: root.process_url()
        background_color: (0.2, 0.6, 0.8, 1) # A distinct color

    Label:
        text: 'Generated Event Links (Scrollable):'
        size_hint_y: None
        height: '30dp'
        halign: 'left'
        valign: 'middle'
        text_size: self.size

    ScrollView:
        id: links_scrollview
        size_hint_y: 0.3 # Adjust as needed, gives it a portion of vertical space
        bar_width: '10dp'
        GridLayout:
            id: links_display
            cols: 1
            size_hint_y: None
            height: self.minimum_height # Important for scrollability
            spacing: '3dp'
            padding: '5dp'
            # Add a placeholder or instruction
            Label:
                id: links_placeholder
                text: 'Links will appear here after processing.'
                size_hint_y: None
                height: '30dp'
                color: (0.7, 0.7, 0.7, 1)

    Label:
        id: status_label
        text: 'Status: Idle. Enter URL and click "Process URL".'
        size_hint_y: None
        height: '60dp' # Allow for a couple lines of text
        text_size: self.width, None # For text wrapping
        halign: 'left'
        valign: 'top' # Align text to top for better reading of multi-line status
        markup: True # To allow simple color changes if needed

    BoxLayout:
        size_hint_y: None
        height: '40dp'
        spacing: '10dp'
        Button:
            id: credit_install_button
            text: '2a. Credit Install'
            disabled: root.credit_install_disabled
            on_release: root.credit_install()
            background_color: (0.3, 0.7, 0.3, 1) # Greenish
        Button:
            id: skip_install_button
            text: '2b. Skip Install'
            disabled: root.skip_install_disabled
            on_release: root.skip_install()
            background_color: (0.8, 0.5, 0.2, 1) # Orangish

    Button:
        id: credit_events_button
        text: '3. Credit Events'
        size_hint_y: None
        height: '40dp'
        disabled: root.credit_events_disabled
        on_release: root.credit_events()
        background_color: (0.7, 0.3, 0.7, 1) # Purplish
"""

class MainLayout(BoxLayout):
    # Properties to manage button states and data
    credit_install_disabled = BooleanProperty(True)
    skip_install_disabled = BooleanProperty(True)
    credit_events_disabled = BooleanProperty(True)

    input_url_text = StringProperty('')
    parsed_install_value = StringProperty(None, allownone=True)
    parsed_event_values = ListProperty([])

    generated_install_link = StringProperty('')
    generated_event_links = ListProperty([])

    base_adjust_url_for_new_links = "https://app.adjust.com/12x88e2e"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Store references to TextInput and Labels for easier access if needed
        # self.url_input_widget = self.ids.url_input
        # self.status_label_widget = self.ids.status_label
        # self.links_display_widget = self.ids.links_display
        self.update_status("Status: Idle. Enter URL and click 'Process URL'.")

    def paste_url(self):
        """Pastes content from clipboard into the URL input field."""
        self.ids.url_input.text = Clipboard.paste()
        self.update_status("URL pasted from clipboard.")

    def process_url(self):
        """Parses the URL from the input field and prepares links."""
        self.input_url_text = self.ids.url_input.text
        if not self.input_url_text:
            self.update_status("[color=ff3333]Error: Input URL is empty.[/color]", is_error=True)
            return

        try:
            parsed_url = urlparse(self.input_url_text)
            query_params = parse_qs(parsed_url.query)

            self.parsed_install_value = None
            if 'install_callback' in query_params:
                # parse_qs returns a list, take the first element
                self.parsed_install_value = query_params['install_callback'][0]

            self.parsed_event_values = []
            for key, values_list in query_params.items():
                if key.startswith('event_callback_'):
                    for value in values_list: # Handle cases where a key might have multiple values
                        self.parsed_event_values.append(value)

            # Generate the actual links for sending requests
            self.generated_install_link = ''
            if self.parsed_install_value:
                params = {'click_callback': f'"{self.parsed_install_value}"'}
                self.generated_install_link = f"{self.base_adjust_url_for_new_links}?{urlencode(params)}"

            self.generated_event_links = []
            for ev_val in self.parsed_event_values:
                params = {'click_callback': f'"{ev_val}"'}
                link = f"{self.base_adjust_url_for_new_links}?{urlencode(params)}"
                self.generated_event_links.append(link)

            # Update UI
            self.ids.links_display.clear_widgets() # Clear previous links
            if not self.generated_event_links:
                self.ids.links_display.add_widget(Label(text='No event_callback parameters found.', size_hint_y=None, height='30dp', color=(0.7,0.7,0.7,1)))
            else:
                for link_text in self.generated_event_links:
                    # Make labels selectable for copying if needed, and wrap text
                    display_text = link_text if len(link_text) < 100 else link_text[:97] + "..."
                    link_label = Label(text=display_text, size_hint_y=None, height='30dp',
                                       text_size=(self.ids.links_display.width - 20, None), # Allow padding
                                       halign='left', valign='middle')
                    self.ids.links_display.add_widget(link_label)

            if not self.parsed_install_value and not self.parsed_event_values:
                 self.update_status(f"[color=ffA500]Warning: No install or event callbacks found in URL.[/color]\nURL: {self.input_url_text[:50]}...")
                 self._reset_button_states_initial()
                 return

            self.update_status(f"URL processed. Install link: {'Yes' if self.generated_install_link else 'No'}. Events found: {len(self.generated_event_links)}.")

            # Enable step 2 buttons, disable step 3
            self.credit_install_disabled = not bool(self.generated_install_link) # Only enable if install link exists
            self.skip_install_disabled = False # Always allow skipping
            self.credit_events_disabled = True

        except Exception as e:
            self.update_status(f"[color=ff3333]Error processing URL: {e}[/color]", is_error=True)
            self._reset_button_states_initial()

    def _reset_button_states_initial(self):
        """Resets buttons to the state after app launch, before processing."""
        self.credit_install_disabled = True
        self.skip_install_disabled = True
        self.credit_events_disabled = True
        self.ids.links_display.clear_widgets()
        self.ids.links_display.add_widget(Label(id='links_placeholder', text='Links will appear here after processing.', size_hint_y=None, height='30dp', color=(0.7,0.7,0.7,1)))


    def credit_install(self):
        """Sends GET request to the install link."""
        if not self.generated_install_link:
            self.update_status("[color=ffA500]No install link generated to credit.[/color]", is_error=True)
            return

        self.update_status(f"Sending install request to: {self.generated_install_link[:70]}...")
        thread = threading.Thread(target=self._send_request_thread,
                                  args=(self.generated_install_link, "Install", "Install Callback"))
        thread.daemon = True
        thread.start()

        # Update button states
        self.credit_install_disabled = True
        self.skip_install_disabled = True
        self.credit_events_disabled = False # Unlock events button

    def skip_install(self):
        """Skips install crediting and enables event crediting."""
        self.update_status("Install skipped. Ready to credit events.")

        # Update button states
        self.credit_install_disabled = True
        self.skip_install_disabled = True
        self.credit_events_disabled = False # Unlock events button

    def credit_events(self):
        """Sends GET requests to all event links."""
        if not self.generated_event_links:
            self.update_status("[color=ffA500]No event links generated to credit.[/color]", is_error=True)
            return

        self.update_status(f"Dispatching {len(self.generated_event_links)} event requests...")

        # For quick succession, launch all in threads
        # Add a small delay between requests if server has rate limits, though prompt says "very quickly"
        for i, link in enumerate(self.generated_event_links):
            event_identifier = f"Event {i+1}" # Simple identifier
            # You could use a portion of the event value as identifier too:
            # event_value_part = self.parsed_event_values[i][:20] # First 20 chars of the original value
            # event_identifier = f"Event {i+1} ({event_value_part}...)"

            thread = threading.Thread(target=self._send_request_thread,
                                      args=(link, "Event", event_identifier))
            thread.daemon = True
            thread.start()
            # time.sleep(0.05) # Optional small delay if needed

        # Optionally disable credit events button after one go, or leave enabled for retry
        # self.credit_events_disabled = True
        self.update_status(f"All {len(self.generated_event_links)} event requests dispatched. Check status for individual results.", append=True)


    def _send_request_thread(self, url, request_type_name, identifier):
        """Helper function to send GET request in a separate thread."""
        try:
            # Using a timeout for requests
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                message = f"[color=33ff33]Success ({response.status_code})[/color]: {request_type_name} '{identifier}'."
                Clock.schedule_once(lambda dt: self.update_status(message, append=True))
            else:
                message = f"[color=ffA500]Failed ({response.status_code})[/color]: {request_type_name} '{identifier}'. URL: {url[:50]}..."
                Clock.schedule_once(lambda dt: self.update_status(message, is_error=True, append=True))
        except requests.exceptions.Timeout:
            message = f"[color=ff3333]Timeout[/color]: Request for {request_type_name} '{identifier}' timed out."
            Clock.schedule_once(lambda dt: self.update_status(message, is_error=True, append=True))
        except requests.exceptions.RequestException as e:
            message = f"[color=ff3333]Error[/color]: {request_type_name} '{identifier}': {str(e)[:100]}" # Keep error message concise
            Clock.schedule_once(lambda dt: self.update_status(message, is_error=True, append=True))

    def update_status(self, message, is_error=False, append=False):
        """Updates the status label. Uses Kivy Clock to ensure it's called on the main thread."""
        current_time = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{current_time}] {message}"

        if append:
            # Keep last N messages if appending, to prevent label from growing too large
            max_log_lines = 5
            current_lines = self.ids.status_label.text.split('\n')
            if len(current_lines) >= max_log_lines :
                current_lines = current_lines[-(max_log_lines-1):] # Keep last N-1 lines

            self.ids.status_label.text = "\n".join(current_lines) + "\n" + formatted_message
        else:
            self.ids.status_label.text = formatted_message

        # Basic color handling (can be enhanced with markup in message itself)
        # if is_error:
        #     self.ids.status_label.color = (1, 0.2, 0.2, 1) # Reddish
        # else:
        #     self.ids.status_label.color = (0.2, 0.8, 0.2, 1) # Greenish (or default Kivy label color)


class AdjustLinkerApp(App):
    def build(self):
        return MainLayout()

if __name__ == '__main__':
    AdjustLinkerApp().run()
